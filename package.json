{"private": true, "scripts": {"start": "umi dev", "build:dev": "cross-env UMI_ENV=dev umi build", "build:qa": "cross-env UMI_ENV=qa umi build", "build:prod": "cross-env UMI_ENV=prod umi build", "postinstall": "umi generate tmp", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "test": "umi-test", "test:coverage": "umi-test --coverage", "analyze": "cross-env ANALYZE=1 umi dev"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,less,md,json}": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "dependencies": {"@ahooksjs/use-request": "^2.8.15", "@ant-design/icons": "^5.3.7", "ahooks": "^3.0.0-alpha.17", "ali-oss": "^6.16.0", "antd": "^4.24.16", "axios": "^1.11.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.11", "echarts": "^5.2.2", "js-cookie": "^3.0.1", "lodash-es": "^4.17.21", "moment": "^2.30.1", "qiankun": "^2.10.16", "qrcode.react": "^4.2.0", "react": "17.x", "react-copy-to-clipboard": "^5.1.0", "react-dom": "17.x", "react-easy-marquee": "^1.2.4", "react-pdf": "^5.7.2", "swiper": "^7.3.1", "umi": "^3.5.20", "umi-request": "^1.4.0", "uuid": "^8.3.2"}, "devDependencies": {"@types/js-cookie": "^3.0.1", "@types/lodash-es": "^4.17.6", "@types/qs": "^6.9.7", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@types/uuid": "^8.3.3", "@umijs/plugin-qiankun": "^2.39.2", "@umijs/preset-react": "1.x", "@umijs/test": "^3.5.20", "cross-env": "^7.0.3", "lint-staged": "^10.0.7", "prettier": "^2.2.0", "typescript": "^4.1.2", "yorkie": "^2.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all", "ie 11"]}}